/* ملف CSS للمكونات المخصصة - WIDDX OMS */

/* متغيرات إضافية للمكونات (تكمل main.css) */
:root {
    /* متغيرات خاصة بالمكونات فقط */
    --component-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    --component-hover-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
    --component-transition: all 0.2s ease;
}

/* مكونات البطاقات المحسنة */
.widdx-card {
    border: none;
    border-radius: var(--card-border-radius);
    box-shadow: var(--component-shadow);
    transition: var(--component-transition);
    overflow: hidden;
}

.widdx-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--component-hover-shadow);
}

.widdx-card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 1rem 1.5rem;
}

.widdx-card-body {
    padding: 1.5rem;
}

/* أزرار محسنة */
.widdx-btn {
    border-radius: var(--button-border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.widdx-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.widdx-btn:hover::before {
    left: 100%;
}

.widdx-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.widdx-btn-success {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    color: white;
}

.widdx-btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #e74c3c);
    color: white;
}

/* حقول الإدخال المحسنة */
.widdx-input {
    border-radius: var(--input-border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 1rem;
}

.widdx-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

/* شريط التنقل المحسن */
.widdx-navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: var(--box-shadow);
    padding: 1rem 0;
}

.widdx-navbar .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.widdx-navbar .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--button-border-radius);
    transition: var(--transition);
    margin: 0 0.25rem;
}

.widdx-navbar .nav-link:hover,
.widdx-navbar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
}

/* جداول محسنة */
.widdx-table {
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.widdx-table thead th {
    background: linear-gradient(135deg, var(--dark-color), #495057);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.widdx-table tbody tr {
    transition: var(--transition);
}

.widdx-table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.widdx-table tbody td {
    padding: 1rem;
    border-color: #e9ecef;
}

/* رسائل التنبيه المحسنة */
.widdx-alert {
    border: none;
    border-radius: var(--card-border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.widdx-alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.widdx-alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.widdx-alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.widdx-alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* مؤشر التحميل المحسن */
.widdx-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none !important; /* إخفاء افتراضي */
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.widdx-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: widdx-spin 1s linear infinite;
}

@keyframes widdx-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* حاوي الرسائل */
.widdx-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9998;
    max-width: 400px;
}

.widdx-message {
    margin-bottom: 10px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
}

/* رسائل التنبيه المحسنة */
.widdx-alert {
    border: none;
    border-radius: var(--card-border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.widdx-alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.widdx-alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.widdx-alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.widdx-alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
    border-left: 4px solid var(--info-color);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .widdx-card-body {
        padding: 1rem;
    }
    
    .widdx-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .widdx-navbar .navbar-brand {
        font-size: 1.25rem;
    }
}

/* مكونات إضافية خاصة بـ components.css */
.widdx-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none !important; /* إخفاء افتراضي */
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* إخفاء جميع عناصر التحميل والـ overlay */
.loading-overlay,
.overlay,
[class*="loading"],
[id*="loading"] {
    display: none !important;
}

/* استثناءات للعناصر التي نريد إظهارها */
.loading-overlay.show,
.widdx-loading.show,
.widdx-loading-overlay.show {
    display: flex !important;
}

.widdx-spinner-large {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid var(--primary-color);
    border-radius: 50%;
    animation: widdx-spin 1s linear infinite;
}

.widdx-table tbody td {
    padding: 1rem;
    border-color: #e9ecef;
}

/* تحسينات للطباعة */
@media print {
    .widdx-navbar,
    .widdx-btn,
    .no-print {
        display: none !important;
    }

    .widdx-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

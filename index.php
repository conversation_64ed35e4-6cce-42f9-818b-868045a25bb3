<?php
/**
 * الصفحة الرئيسية لنظام WIDDX OMS
 * تم تحديثها لاستخدام Layout الموحد الجديد
 */

// تحميل النظام
require_once 'config/autoload.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('الرئيسية')
    ->setPageDescription('الصفحة الرئيسية لنظام إدارة الطلبيات WIDDX OMS')
    ->setCurrentPage('index')
    ->addBreadcrumb('الرئيسية', null, 'fas fa-home')
    ->showStatusBar(true);

try {
    // إنشاء مثيلات من الكلاسات
    if (class_exists('Customer')) {
        $customerManager = new Customer();
        $customerStats = $customerManager->getStats();
        $activeCustomers = $customerManager->getActiveCustomers();
    } else {
        // استخدام الطريقة القديمة كبديل
        $db = getDatabase();
        $customerStats = [
            'total_customers' => 0,
            'active_customers' => 0,
            'new_this_month' => 0
        ];
        $activeCustomers = [];

        // جلب إحصائيات العملاء
        $db->query("SELECT COUNT(*) as count FROM customers");
        $customerStats['total_customers'] = $db->single()['count'] ?? 0;
    }

    if (class_exists('Order')) {
        $orderManager = new Order();
        $orderStats = $orderManager->getStats();
    } else {
        // استخدام الطريقة القديمة كبديل
        $db = getDatabase();
        $orderStats = [
            'total_orders' => 0,
            'pending_orders' => 0,
            'processing_orders' => 0,
            'completed_orders' => 0
        ];

        // جلب إحصائيات الطلبيات
        $db->query("SELECT COUNT(*) as count FROM orders");
        $orderStats['total_orders'] = $db->single()['count'] ?? 0;

        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
        $orderStats['pending_orders'] = $db->single()['count'] ?? 0;

        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'processing'");
        $orderStats['processing_orders'] = $db->single()['count'] ?? 0;

        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'completed'");
        $orderStats['completed_orders'] = $db->single()['count'] ?? 0;
    }
} catch (Exception $e) {
    // في حالة حدوث خطأ، استخدم قيم افتراضية
    $customerStats = ['total_customers' => 0, 'active_customers' => 0, 'new_this_month' => 0];
    $orderStats = ['total_orders' => 0, 'pending_orders' => 0, 'processing_orders' => 0, 'completed_orders' => 0];
    $activeCustomers = [];
}

// بدء Layout
$layout->startLayout();
?>
    <!-- المحتوى الرئيسي -->
    <div class="mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header text-center">
                        <h2><i class="fas fa-home"></i> مرحباً بك في نظام إدارة الطلبيات</h2>
                    </div>
                    <div class="card-body text-center">
                        <p class="lead">نظام شامل لإدارة العملاء والمنتجات والطلبيات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">

            <div class="col-md-2 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3><?php echo $customerStats['total_customers'] ?? 0; ?></h3>
                        <p>العملاء</p>
                    </div>
                </div>
            </div>

            <div class="col-md-2 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['total_orders'] ?? 0; ?></h3>
                        <p>إجمالي الطلبيات</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['pending_orders'] ?? 0; ?></h3>
                        <p>طلبيات معلقة</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['processing_orders'] ?? 0; ?></h3>
                        <p>قيد التنفيذ</p>
                    </div>
                </div>
            </div>

            <div class="col-md-2 mb-3">
                <div class="card stats-card bg-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?php echo $orderStats['completed_orders'] ?? 0; ?></h3>
                        <p>مكتملة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-plus feature-icon"></i>
                        <h5 class="card-title">إضافة عميل جديد</h5>
                        <a href="add_customer.php" class="btn btn-primary">إضافة عميل</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle feature-icon"></i>
                        <h5 class="card-title">إضافة طلبية جديدة</h5>
                        <a href="add_order.php" class="btn btn-primary">إضافة طلبية</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-list feature-icon"></i>
                        <h5 class="card-title">عرض جميع الطلبيات</h5>
                        <a href="view_orders.php" class="btn btn-primary">عرض الطلبيات</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users-cog feature-icon"></i>
                        <h5 class="card-title">إدارة العملاء</h5>
                        <a href="manage_customers.php" class="btn btn-primary">إدارة العملاء</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة تحكم العملاء والطلبيات -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt"></i> لوحة تحكم العملاء والطلبيات</h5>
            </div>
            <div class="card-body">

                <?php if (count($activeCustomers) > 0): ?>
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>يتم عرض العملاء الذين لديهم طلبيات معلقة أو قيد التنفيذ فقط</strong>
                    </div>
                    <div class="accordion" id="customersAccordion">
                        <?php foreach ($activeCustomers as $index => $customer): ?>
                            <div class="accordion-item mb-2">
                                <h2 class="accordion-header" id="heading<?php echo $customer['id']; ?>">
                                    <button class="accordion-button collapsed customer-button" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target="#collapse<?php echo $customer['id']; ?>"
                                            aria-expanded="false"
                                            aria-controls="collapse<?php echo $customer['id']; ?>"
                                            onclick="loadCustomerOrders(<?php echo $customer['id']; ?>)">
                                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                            <div>
                                                <i class="fas fa-user me-2"></i>
                                                <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                                <?php if (!empty($customer['phone'])): ?>
                                                    <small class="text-muted ms-2">
                                                        <i class="fas fa-phone"></i> <?php echo htmlspecialchars($customer['phone']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="customer-stats">
                                                <?php
                                                $active_orders = $customer['pending_count'] + $customer['processing_count'];
                                                ?>
                                                <span class="badge bg-primary me-1">
                                                    <i class="fas fa-exclamation-circle"></i> <?php echo $active_orders; ?> طلبية نشطة
                                                </span>
                                                <?php if ($customer['pending_count'] > 0): ?>
                                                    <span class="badge bg-warning text-dark me-1">
                                                        <i class="fas fa-clock"></i> معلقة: <?php echo $customer['pending_count']; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php if ($customer['processing_count'] > 0): ?>
                                                    <span class="badge bg-info me-1">
                                                        <i class="fas fa-cogs"></i> قيد التنفيذ: <?php echo $customer['processing_count']; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <span class="badge bg-secondary me-1">
                                                    <i class="fas fa-list"></i> إجمالي: <?php echo $customer['orders_count']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $customer['id']; ?>"
                                     class="accordion-collapse collapse"
                                     aria-labelledby="heading<?php echo $customer['id']; ?>"
                                     data-bs-parent="#customersAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6><i class="fas fa-list"></i> طلبيات العميل</h6>
                                            <a href="add_order.php?customer_id=<?php echo $customer['id']; ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> إضافة طلبية جديدة
                                            </a>
                                        </div>

                                        <div id="orders-container-<?php echo $customer['id']; ?>">
                                            <div class="text-center py-3" id="loading-<?php echo $customer['id']; ?>" style="display: none;">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                            </div>
                                            <div class="text-center py-3 text-muted">
                                                <i class="fas fa-mouse-pointer"></i>
                                                انقر لعرض طلبيات العميل
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">ممتاز! لا توجد طلبيات معلقة أو قيد التنفيذ</h5>
                        <p class="text-muted">جميع الطلبيات مكتملة أو لا توجد طلبيات نشطة حالياً</p>
                        <div class="mt-3">
                            <a href="add_order.php" class="btn btn-primary me-2">
                                <i class="fas fa-plus-circle"></i> إضافة طلبية جديدة
                            </a>
                            <a href="view_orders.php" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> عرض جميع الطلبيات
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php
// إضافة JavaScript مخصص للصفحة
$layout->addInlineJS('
    // وظائف خاصة بالصفحة الرئيسية
    function loadCustomerOrders(customerId) {
        if (typeof widdxSystem !== "undefined") {
            widdxSystem.loadCustomerOrders(customerId);
        } else {
            // طريقة بديلة محسنة
            const container = document.getElementById(`orders-container-${customerId}`);
            const loadingElement = document.getElementById(`loading-${customerId}`);

            if (container) {
                // إظهار مؤشر التحميل وإخفاء المحتوى الافتراضي
                container.innerHTML = `
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <div class="mt-2 text-muted">جاري تحميل طلبيات العميل...</div>
                    </div>
                `;

                fetch(`get_customer_orders.php?customer_id=${customerId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(data => {
                        container.innerHTML = data;
                    })
                    .catch(error => {
                        console.error("Error:", error);
                        container.innerHTML = `
                            <div class="alert alert-danger text-center">
                                <i class="fas fa-exclamation-triangle"></i>
                                حدث خطأ في تحميل الطلبيات
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="loadCustomerOrders(${customerId})">
                                        <i class="fas fa-redo"></i> إعادة المحاولة
                                    </button>
                                </div>
                            </div>
                        `;
                    });
            }
        }
    }

    function updateOrderStatus(orderId, status) {
        if (typeof widdxSystem !== "undefined") {
            widdxSystem.updateOrderStatus(orderId, status);
        } else {
            // طريقة بديلة محسنة
            showGlobalLoading("جاري تحديث حالة الطلبية...");

            const formData = new FormData();
            formData.append("order_id", orderId);
            formData.append("status", status);
            formData.append("update_status", "1");

            fetch("update_order_status.php", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideGlobalLoading();
                if (data.success) {
                    showGlobalMessage("تم تحديث حالة الطلبية بنجاح", "success");
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showGlobalMessage(data.message || "حدث خطأ في تحديث الحالة", "error");
                }
            })
            .catch(error => {
                hideGlobalLoading();
                console.error("Error:", error);
                showGlobalMessage("حدث خطأ في الاتصال", "error");
            });
        }
    }
');

// إنهاء Layout
$layout->endLayout();
?>

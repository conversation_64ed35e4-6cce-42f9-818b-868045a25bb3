<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'نظام إدارة الطلبيات'; ?> - WIDDX OMS</title>

    <!-- Meta tags for SEO and performance -->
    <meta name="description" content="<?php echo $pageDescription ?? 'نظام إدارة الطلبيات WIDDX OMS - نظام شامل لإدارة العملاء والطلبيات'; ?>">
    <meta name="keywords" content="إدارة الطلبيات, نظام إدارة, عملاء, طلبيات, WIDDX">
    <meta name="author" content="WIDDX Team">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="alternate icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
    <meta name="theme-color" content="#667eea">

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style"
          integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/css/components.css" as="style">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Custom CSS Files -->
    <link href="assets/css/main.css" rel="stylesheet" type="text/css">
    <link href="assets/css/components.css" rel="stylesheet" type="text/css">

    <!-- إصلاح مشكلة الغطاء الأسود -->
    <script src="fix_overlay.js" defer></script>

    <!-- تحسينات CSS إضافية -->
    <style>
        /* إصلاحات سريعة للتوافق */
        .widdx-card {
            background: white;
            margin-bottom: 1.5rem;
        }

        .widdx-btn {
            display: inline-block;
            text-decoration: none;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            user-select: none;
        }

        .widdx-loading {
            display: none !important;
        }

        #global-loading {
            display: none !important;
        }

        .widdx-messages {
            pointer-events: none;
        }

        .widdx-message {
            pointer-events: auto;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>

    <!-- Additional CSS if provided -->
    <?php if (isset($additionalCSS) && is_array($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo htmlspecialchars($css); ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Custom styles for specific pages -->
    <?php if (isset($customStyles) && !empty($customStyles)): ?>
    <style>
        <?php echo $customStyles; ?>
    </style>
    <?php endif; ?>

    <!-- Page-specific meta tags -->
    <?php if (isset($pageMetaTags) && is_array($pageMetaTags)): ?>
        <?php foreach ($pageMetaTags as $name => $content): ?>
            <meta name="<?php echo htmlspecialchars($name); ?>" content="<?php echo htmlspecialchars($content); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- شريط التنقل المحسن -->
    <nav class="navbar navbar-expand-lg navbar-dark widdx-navbar sticky-top">
        <div class="container">
            <!-- شعار النظام -->
            <a class="navbar-brand fw-bold" href="index.php" title="العودة للصفحة الرئيسية">
                <i class="fas fa-box me-2"></i>
                <span class="d-none d-sm-inline">WIDDX OMS</span>
            </a>

            <!-- زر القائمة للشاشات الصغيرة -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- قائمة التنقل -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'index' ? 'active' : ''; ?>"
                           href="index.php" title="الصفحة الرئيسية">
                            <i class="fas fa-home me-1"></i>
                            <span class="d-none d-lg-inline">الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'add_customer' ? 'active' : ''; ?>"
                           href="add_customer.php" title="إضافة عميل جديد">
                            <i class="fas fa-user-plus me-1"></i>
                            <span class="d-none d-lg-inline">إضافة عميل</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'add_order' ? 'active' : ''; ?>"
                           href="add_order.php" title="إضافة طلبية جديدة">
                            <i class="fas fa-plus-circle me-1"></i>
                            <span class="d-none d-lg-inline">إضافة طلبية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'view_orders' ? 'active' : ''; ?>"
                           href="view_orders.php" title="عرض جميع الطلبيات">
                            <i class="fas fa-list me-1"></i>
                            <span class="d-none d-lg-inline">عرض الطلبيات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'manage_customers' ? 'active' : ''; ?>"
                           href="manage_customers.php" title="إدارة العملاء">
                            <i class="fas fa-users-cog me-1"></i>
                            <span class="d-none d-lg-inline">إدارة العملاء</span>
                        </a>
                    </li>

                    <!-- قائمة منسدلة للتقارير والأدوات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false" title="أدوات النظام">
                            <i class="fas fa-tools me-1"></i>
                            <span class="d-none d-lg-inline">أدوات</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <li><h6 class="dropdown-header">أدوات النظام</h6></li>
                            <li><a class="dropdown-item" href="system_check.php">
                                <i class="fas fa-check-circle text-success me-2"></i> فحص النظام
                            </a></li>
                            <li><a class="dropdown-item" href="update_database.php">
                                <i class="fas fa-database text-info me-2"></i> تحديث قاعدة البيانات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">تصدير البيانات</h6></li>
                            <li><a class="dropdown-item" href="#" onclick="exportData('excel', 'orders')">
                                <i class="fas fa-file-excel text-success me-2"></i> تصدير الطلبيات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportData('excel', 'customers')">
                                <i class="fas fa-file-excel text-success me-2"></i> تصدير العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="printPage()">
                                <i class="fas fa-print text-secondary me-2"></i> طباعة الصفحة
                            </a></li>
                        </ul>
                    </li>
                </ul>

                <!-- معلومات المستخدم والإشعارات -->
                <ul class="navbar-nav">
                    <!-- عداد الإشعارات المحسن -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false" title="الإشعارات">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                  id="notifications-count" style="display: none;">
                                0
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="min-width: 300px;">
                            <h6 class="dropdown-header d-flex justify-content-between align-items-center">
                                <span>الإشعارات</span>
                                <button class="btn btn-sm btn-outline-secondary" onclick="markAllNotificationsRead()" title="تعليم الكل كمقروء">
                                    <i class="fas fa-check-double"></i>
                                </button>
                            </h6>
                            <div class="dropdown-divider"></div>
                            <div id="notifications-list" style="max-height: 300px; overflow-y: auto;">
                                <div class="dropdown-item text-muted text-center py-3">
                                    <i class="fas fa-info-circle me-2"></i> لا توجد إشعارات جديدة
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center small" href="#" onclick="loadAllNotifications()">
                                <i class="fas fa-eye me-1"></i> عرض جميع الإشعارات
                            </a>
                        </div>
                    </li>

                    <!-- معلومات سريعة محسنة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="quickStatsDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false" title="إحصائيات سريعة">
                            <i class="fas fa-chart-bar"></i>
                            <span class="d-none d-xl-inline ms-1">إحصائيات</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end" aria-labelledby="quickStatsDropdown" style="min-width: 250px;">
                            <h6 class="dropdown-header">إحصائيات اليوم</h6>
                            <div class="dropdown-divider"></div>
                            <div class="px-3 py-2">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><i class="fas fa-shopping-cart text-primary me-2"></i>طلبيات جديدة</span>
                                    <span class="badge bg-primary" id="today-orders">0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><i class="fas fa-users text-success me-2"></i>عملاء جدد</span>
                                    <span class="badge bg-success" id="today-customers">0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-money-bill text-warning me-2"></i>المبيعات</span>
                                    <span class="badge bg-warning text-dark" id="today-sales">0 ج.م</span>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center small" href="index.php">
                                <i class="fas fa-chart-line me-1"></i> عرض التفاصيل
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- شريط الحالة (اختياري) -->
    <?php if (isset($showStatusBar) && $showStatusBar): ?>
    <div class="bg-light border-bottom py-1">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        آخر تحديث: <span id="last-update-time"><?php echo date('H:i:s'); ?></span>
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <span id="connection-status" class="badge bg-success">متصل</span>
                    </small>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- حاوي الرسائل العامة المحسن -->
    <div id="global-messages-container" class="position-relative" style="z-index: 1050;"></div>

    <!-- مؤشر التحميل العام المحسن -->
    <div id="global-loading" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center"
         style="background-color: rgba(0,0,0,0.7); z-index: 9999; display: none;">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <h5 class="fw-light">جاري التحميل...</h5>
            <p class="small text-light opacity-75" id="loading-message">يرجى الانتظار</p>
        </div>
    </div>

    <!-- مؤشر التقدم العام -->
    <div id="global-progress" class="position-fixed top-0 start-0 w-100" style="z-index: 9998; display: none;">
        <div class="progress" style="height: 3px; border-radius: 0;">
            <div class="progress-bar bg-primary" role="progressbar" style="width: 0%"></div>
        </div>
    </div>

    <!-- بداية المحتوى الرئيسي -->
    <main class="main-content">
        <?php if (isset($showBreadcrumb) && $showBreadcrumb): ?>
            <!-- مسار التنقل المحسن -->
            <div class="container mt-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-light rounded p-3">
                        <li class="breadcrumb-item">
                            <a href="index.php" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <?php if (isset($breadcrumbs) && is_array($breadcrumbs)): ?>
                            <?php foreach ($breadcrumbs as $breadcrumb): ?>
                                <?php if (isset($breadcrumb['url']) && !empty($breadcrumb['url'])): ?>
                                    <li class="breadcrumb-item">
                                        <a href="<?php echo htmlspecialchars($breadcrumb['url']); ?>" class="text-decoration-none">
                                            <?php if (isset($breadcrumb['icon'])): ?>
                                                <i class="<?php echo htmlspecialchars($breadcrumb['icon']); ?> me-1"></i>
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php if (isset($breadcrumb['icon'])): ?>
                                            <i class="<?php echo htmlspecialchars($breadcrumb['icon']); ?> me-1"></i>
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ol>
                </nav>
            </div>
        <?php endif; ?>

    <!-- JavaScript محسن للتحقق من الإشعارات والإحصائيات -->
    <script>
        // متغيرات عامة
        let updateInterval;
        let connectionStatus = true;

        // تحديث الإشعارات والإحصائيات السريعة (نسخة Header)
        async function updateQuickStatsHeader() {
            try {
                showProgress(20);
                const response = await fetch('api/quick_stats.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                showProgress(60);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                showProgress(80);

                if (data.success) {
                    // تحديث الإحصائيات
                    updateElement('today-orders', data.stats.today_orders || 0);
                    updateElement('today-customers', data.stats.today_customers || 0);
                    updateElement('today-sales', (data.stats.today_sales || 0) + ' ج.م');

                    // تحديث عداد الإشعارات
                    updateNotifications(data.notifications || []);

                    // تحديث حالة الاتصال
                    updateConnectionStatus(true);

                    // تحديث وقت آخر تحديث
                    updateElement('last-update-time', new Date().toLocaleTimeString('ar-EG'));
                } else {
                    console.warn('Quick stats update failed:', data.message);
                }

                showProgress(100);
                setTimeout(hideProgress, 500);

            } catch (error) {
                console.error('Error updating quick stats:', error);
                updateConnectionStatus(false);
                hideProgress();
            }
        }

        // استخدام الوظيفة الموحدة من widdx-core.js إذا كانت متوفرة
        function updateQuickStats() {
            // التحقق من وجود widdx-core.js
            if (typeof window.widdxCoreLoaded !== 'undefined' && window.widdxCoreLoaded) {
                // استخدام الوظيفة من widdx-core.js
                if (typeof window.widdxUpdateQuickStats === 'function') {
                    return window.widdxUpdateQuickStats();
                }
            }
            // استخدام النسخة المحلية
            return updateQuickStatsHeader();
        }

        // تحديث عنصر في الصفحة
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        // تحديث الإشعارات
        function updateNotifications(notifications) {
            const badge = document.getElementById('notifications-count');
            const list = document.getElementById('notifications-list');

            if (notifications.length > 0) {
                badge.textContent = notifications.length;
                badge.style.display = 'block';

                // تحديث قائمة الإشعارات
                if (list) {
                    list.innerHTML = notifications.map(notification => `
                        <div class="dropdown-item py-2 ${notification.read ? '' : 'bg-light'}">
                            <div class="d-flex align-items-start">
                                <i class="${notification.icon || 'fas fa-info-circle'} text-${notification.type || 'info'} me-2 mt-1"></i>
                                <div class="flex-grow-1">
                                    <div class="fw-bold small">${notification.title}</div>
                                    <div class="text-muted small">${notification.message}</div>
                                    <div class="text-muted small">${notification.time}</div>
                                </div>
                            </div>
                        </div>
                    `).join('');
                }
            } else {
                badge.style.display = 'none';
                if (list) {
                    list.innerHTML = `
                        <div class="dropdown-item text-muted text-center py-3">
                            <i class="fas fa-info-circle me-2"></i> لا توجد إشعارات جديدة
                        </div>
                    `;
                }
            }
        }

        // تحديث حالة الاتصال
        function updateConnectionStatus(isConnected) {
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                if (isConnected) {
                    statusElement.className = 'badge bg-success';
                    statusElement.textContent = 'متصل';
                } else {
                    statusElement.className = 'badge bg-danger';
                    statusElement.textContent = 'غير متصل';
                }
            }
            connectionStatus = isConnected;
        }

        // إظهار شريط التقدم
        function showProgress(percentage) {
            const progressContainer = document.getElementById('global-progress');
            const progressBar = progressContainer?.querySelector('.progress-bar');

            if (progressContainer && progressBar) {
                progressContainer.style.display = 'block';
                progressBar.style.width = percentage + '%';
            }
        }

        // إخفاء شريط التقدم
        function hideProgress() {
            const progressContainer = document.getElementById('global-progress');
            if (progressContainer) {
                progressContainer.style.display = 'none';
                const progressBar = progressContainer.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = '0%';
                }
            }
        }

        // وظائف إضافية للإشعارات
        function markAllNotificationsRead() {
            // تنفيذ تعليم جميع الإشعارات كمقروءة
            fetch('api/mark_notifications_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }).then(() => updateQuickStats());
        }

        // دالة إخفاء جميع مؤشرات التحميل
        function hideProgress() {
            // إخفاء جميع مؤشرات التحميل الدائرية
            const spinners = document.querySelectorAll('.spinner-border');
            spinners.forEach(spinner => {
                const parent = spinner.closest('.text-center');
                if (parent) {
                    parent.style.display = 'none';
                }
            });

            // إخفاء شريط التقدم
            const progressContainer = document.getElementById('global-progress');
            if (progressContainer) {
                progressContainer.style.display = 'none';
                const progressBar = progressContainer.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = '0%';
                }
            }
        }

        // دالة إخفاء مؤشر التحميل العام
        function hideGlobalLoading() {
            const loadingElement = document.getElementById('global-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
                document.body.style.overflow = '';
            }
        }

        function loadAllNotifications() {
            // فتح صفحة جميع الإشعارات
            window.location.href = 'notifications.php';
        }

        // وظائف التصدير والطباعة
        function exportData(format, type) {
            const url = `export.php?format=${format}&type=${type}`;
            window.open(url, '_blank');
        }

        function printPage() {
            window.print();
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إخفاء أي مؤشرات تحميل قد تكون ظاهرة فوراً
            hideProgress();
            hideGlobalLoading();

            // إخفاء جميع عناصر التحميل المحتملة
            const loadingElements = document.querySelectorAll('.widdx-loading, #global-loading, .loading-overlay');
            loadingElements.forEach(element => {
                element.style.display = 'none';
            });

            // تأخير قصير لضمان تحميل جميع العناصر
            setTimeout(() => {
                // تحديث فوري
                updateQuickStats();
            }, 1000);

            // تحديث دوري كل دقيقة
            updateInterval = setInterval(updateQuickStats, 60000);

            // تنظيف عند إغلاق الصفحة
            window.addEventListener('beforeunload', function() {
                if (updateInterval) {
                    clearInterval(updateInterval);
                }
            });

            // إضافة مستمع للنقر على الروابط لإخفاء مؤشر التحميل
            document.addEventListener('click', function(e) {
                // إذا كان النقر على رابط، أخفي مؤشر التحميل بعد فترة قصيرة
                if (e.target.tagName === 'A' || e.target.closest('a')) {
                    setTimeout(hideProgress, 100);
                }
            });
        });
    </script>

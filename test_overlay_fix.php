<?php
/**
 * اختبار إصلاح مشكلة الغطاء الأسود
 */

// تحميل النظام
require_once 'config/autoload.php';
require_once 'includes/layout.php';

// إنشاء مثيل Layout
$layout = createLayout()
    ->setPageTitle('اختبار إصلاح الغطاء الأسود')
    ->setPageDescription('صفحة اختبار لإصلاح مشكلة الغطاء الأسود')
    ->setCurrentPage('test')
    ->addBreadcrumb('اختبار الإصلاح', null, 'fas fa-bug');

// بدء Layout
$layout->startLayout();
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4><i class="fas fa-check-circle me-2"></i>اختبار إصلاح الغطاء الأسود</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>هذه الصفحة لاختبار إصلاح مشكلة الغطاء الأسود</strong>
                        <br>إذا كنت ترى هذه الرسالة بوضوح، فقد تم حل المشكلة بنجاح.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-tools text-primary me-2"></i>أدوات الاختبار</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="testShowLoading()">
                                    <i class="fas fa-spinner me-2"></i>اختبار مؤشر التحميل
                                </button>
                                <button class="btn btn-warning" onclick="testCreateOverlay()">
                                    <i class="fas fa-layer-group me-2"></i>اختبار إنشاء overlay
                                </button>
                                <button class="btn btn-success" onclick="fixOverlayIssue()">
                                    <i class="fas fa-magic me-2"></i>تشغيل إصلاح الغطاء
                                </button>
                                <button class="btn btn-info" onclick="showSystemInfo()">
                                    <i class="fas fa-info me-2"></i>معلومات النظام
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5><i class="fas fa-clipboard-list text-success me-2"></i>نتائج الاختبار</h5>
                            <div id="test-results" class="border rounded p-3 bg-light" style="min-height: 200px;">
                                <div class="text-muted text-center">
                                    <i class="fas fa-play-circle fa-2x mb-2"></i>
                                    <br>انقر على أحد الأزرار لبدء الاختبار
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row">
                        <div class="col-12">
                            <h5><i class="fas fa-chart-line text-info me-2"></i>حالة النظام</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المكون</th>
                                            <th>الحالة</th>
                                            <th>التفاصيل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><i class="fas fa-file-code me-2"></i>fix_overlay.js</td>
                                            <td><span id="fix-overlay-status" class="badge bg-secondary">جاري الفحص...</span></td>
                                            <td id="fix-overlay-details">-</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-cogs me-2"></i>WIDDXCore</td>
                                            <td><span id="widdx-core-status" class="badge bg-secondary">جاري الفحص...</span></td>
                                            <td id="widdx-core-details">-</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-layer-group me-2"></i>عناصر Overlay</td>
                                            <td><span id="overlay-status" class="badge bg-secondary">جاري الفحص...</span></td>
                                            <td id="overlay-details">-</td>
                                        </tr>
                                        <tr>
                                            <td><i class="fas fa-spinner me-2"></i>مؤشرات التحميل</td>
                                            <td><span id="loading-status" class="badge bg-secondary">جاري الفحص...</span></td>
                                            <td id="loading-details">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// وظائف الاختبار
function addTestResult(message, type = 'info') {
    const resultsDiv = document.getElementById('test-results');
    const timestamp = new Date().toLocaleTimeString('ar-EG');
    const icon = {
        'success': 'fas fa-check-circle text-success',
        'error': 'fas fa-times-circle text-danger',
        'warning': 'fas fa-exclamation-triangle text-warning',
        'info': 'fas fa-info-circle text-info'
    }[type] || 'fas fa-info-circle text-info';
    
    const resultHtml = `
        <div class="mb-2 p-2 border-start border-3 border-${type === 'error' ? 'danger' : type}">
            <small class="text-muted">${timestamp}</small><br>
            <i class="${icon} me-2"></i>${message}
        </div>
    `;
    
    if (resultsDiv.innerHTML.includes('انقر على أحد الأزرار')) {
        resultsDiv.innerHTML = resultHtml;
    } else {
        resultsDiv.innerHTML += resultHtml;
    }
    
    resultsDiv.scrollTop = resultsDiv.scrollHeight;
}

function testShowLoading() {
    addTestResult('بدء اختبار مؤشر التحميل...', 'info');
    
    if (typeof showGlobalLoading === 'function') {
        showGlobalLoading('اختبار مؤشر التحميل...');
        addTestResult('تم إظهار مؤشر التحميل', 'success');
        
        setTimeout(() => {
            if (typeof hideGlobalLoading === 'function') {
                hideGlobalLoading();
                addTestResult('تم إخفاء مؤشر التحميل', 'success');
            }
        }, 2000);
    } else {
        addTestResult('وظيفة showGlobalLoading غير متوفرة', 'error');
    }
}

function testCreateOverlay() {
    addTestResult('بدء اختبار إنشاء overlay...', 'info');
    
    // إنشاء overlay مؤقت
    const overlay = document.createElement('div');
    overlay.id = 'test-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
    `;
    overlay.innerHTML = '<div><h3>اختبار Overlay</h3><p>سيتم إزالة هذا العنصر تلقائياً</p></div>';
    
    document.body.appendChild(overlay);
    addTestResult('تم إنشاء overlay اختباري', 'warning');
    
    setTimeout(() => {
        if (typeof fixOverlayIssue === 'function') {
            fixOverlayIssue();
            addTestResult('تم تشغيل إصلاح الغطاء', 'success');
        }
        
        // إزالة يدوية كبديل
        const testOverlay = document.getElementById('test-overlay');
        if (testOverlay) {
            testOverlay.remove();
            addTestResult('تم إزالة overlay الاختباري', 'success');
        }
    }, 3000);
}

function showSystemInfo() {
    addTestResult('جمع معلومات النظام...', 'info');
    
    const info = {
        'متصفح': navigator.userAgent.split(' ')[0],
        'حجم الشاشة': `${window.innerWidth}x${window.innerHeight}`,
        'وقت التحميل': performance.now().toFixed(2) + 'ms',
        'عدد العناصر': document.querySelectorAll('*').length
    };
    
    Object.entries(info).forEach(([key, value]) => {
        addTestResult(`${key}: ${value}`, 'info');
    });
}

// فحص حالة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // فحص fix_overlay.js
    if (typeof fixOverlayIssue === 'function') {
        document.getElementById('fix-overlay-status').className = 'badge bg-success';
        document.getElementById('fix-overlay-status').textContent = 'متوفر';
        document.getElementById('fix-overlay-details').textContent = 'تم تحميل الإصلاح بنجاح';
    } else {
        document.getElementById('fix-overlay-status').className = 'badge bg-danger';
        document.getElementById('fix-overlay-status').textContent = 'غير متوفر';
        document.getElementById('fix-overlay-details').textContent = 'لم يتم تحميل الإصلاح';
    }
    
    // فحص WIDDXCore
    if (typeof WIDDXCore !== 'undefined') {
        document.getElementById('widdx-core-status').className = 'badge bg-success';
        document.getElementById('widdx-core-status').textContent = 'متوفر';
        document.getElementById('widdx-core-details').textContent = 'تم تحميل النظام الأساسي';
    } else {
        document.getElementById('widdx-core-status').className = 'badge bg-warning';
        document.getElementById('widdx-core-status').textContent = 'غير متوفر';
        document.getElementById('widdx-core-details').textContent = 'النظام الأساسي غير محمل';
    }
    
    // فحص عناصر Overlay
    const overlays = document.querySelectorAll('.widdx-loading, .loading-overlay, #global-loading');
    const visibleOverlays = Array.from(overlays).filter(el => {
        const style = window.getComputedStyle(el);
        return style.display !== 'none';
    });
    
    if (visibleOverlays.length === 0) {
        document.getElementById('overlay-status').className = 'badge bg-success';
        document.getElementById('overlay-status').textContent = 'نظيف';
        document.getElementById('overlay-details').textContent = 'لا توجد عناصر overlay ظاهرة';
    } else {
        document.getElementById('overlay-status').className = 'badge bg-warning';
        document.getElementById('overlay-status').textContent = 'يحتاج إصلاح';
        document.getElementById('overlay-details').textContent = `${visibleOverlays.length} عنصر overlay ظاهر`;
    }
    
    // فحص مؤشرات التحميل
    const loadingElements = document.querySelectorAll('.spinner-border, [class*="loading"]');
    const visibleLoading = Array.from(loadingElements).filter(el => {
        const style = window.getComputedStyle(el);
        return style.display !== 'none';
    });
    
    if (visibleLoading.length === 0) {
        document.getElementById('loading-status').className = 'badge bg-success';
        document.getElementById('loading-status').textContent = 'نظيف';
        document.getElementById('loading-details').textContent = 'لا توجد مؤشرات تحميل ظاهرة';
    } else {
        document.getElementById('loading-status').className = 'badge bg-info';
        document.getElementById('loading-status').textContent = 'عادي';
        document.getElementById('loading-details').textContent = `${visibleLoading.length} مؤشر تحميل موجود`;
    }
    
    addTestResult('تم فحص حالة النظام بنجاح', 'success');
});
</script>

<?php
// إنهاء Layout
$layout->endLayout();
?>

/**
 * إصلاح مشكلة الغطاء الأسود في نظام WIDDX OMS
 * يتم تشغيل هذا الملف لإزالة أي عناصر overlay أو loading قد تسبب مشاكل
 */

(function() {
    'use strict';
    
    console.log('🔧 بدء إصلاح مشكلة الغطاء الأسود...');
    
    // وظيفة إزالة جميع عناصر التحميل والـ overlay
    function removeAllOverlays() {
        // قائمة بجميع العناصر المحتملة التي قد تسبب الغطاء الأسود
        const selectors = [
            '.widdx-loading',
            '.widdx-loading-overlay', 
            '#global-loading',
            '.loading-overlay',
            '.overlay',
            '.modal-backdrop',
            '[style*="rgba(0,0,0"]',
            '[style*="rgba(0, 0, 0"]',
            '[style*="background: rgba(0,0,0"]',
            '[style*="background-color: rgba(0,0,0"]',
            '[class*="loading"]',
            '[id*="loading"]'
        ];
        
        let removedCount = 0;
        
        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // فحص إذا كان العنصر يحتوي على خلفية داكنة
                    const style = window.getComputedStyle(element);
                    const bgColor = style.backgroundColor;
                    const position = style.position;
                    const zIndex = parseInt(style.zIndex) || 0;
                    
                    // إذا كان العنصر overlay محتمل
                    if (
                        (position === 'fixed' || position === 'absolute') &&
                        (bgColor.includes('rgba(0, 0, 0') || bgColor.includes('rgba(0,0,0')) &&
                        zIndex > 1000
                    ) {
                        element.style.display = 'none';
                        element.remove();
                        removedCount++;
                        console.log('🗑️ تم إزالة عنصر overlay:', element);
                    }
                    
                    // إزالة عناصر التحميل
                    if (element.classList.contains('widdx-loading') || 
                        element.classList.contains('loading-overlay') ||
                        element.id === 'global-loading') {
                        element.style.display = 'none';
                        removedCount++;
                        console.log('🗑️ تم إخفاء عنصر تحميل:', element);
                    }
                });
            } catch (error) {
                console.warn('⚠️ خطأ في معالجة selector:', selector, error);
            }
        });
        
        return removedCount;
    }
    
    // وظيفة إزالة الخصائص المشكوك فيها من body
    function fixBodyStyles() {
        const body = document.body;
        const html = document.documentElement;
        
        // إزالة overflow: hidden من body و html
        body.style.overflow = '';
        html.style.overflow = '';
        
        // إزالة أي خلفيات داكنة
        if (body.style.backgroundColor && body.style.backgroundColor.includes('rgba(0, 0, 0')) {
            body.style.backgroundColor = '';
        }
        
        console.log('✅ تم إصلاح أنماط body');
    }
    
    // وظيفة إزالة backdrop من Bootstrap
    function removeBootstrapBackdrops() {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            backdrop.remove();
            console.log('🗑️ تم إزالة modal backdrop');
        });
    }
    
    // وظيفة التنظيف الشامل
    function fullCleanup() {
        console.log('🧹 بدء التنظيف الشامل...');
        
        const removedOverlays = removeAllOverlays();
        fixBodyStyles();
        removeBootstrapBackdrops();
        
        // إزالة أي عناصر بـ z-index عالي وخلفية داكنة
        const highZIndexElements = Array.from(document.querySelectorAll('*')).filter(el => {
            const style = window.getComputedStyle(el);
            const zIndex = parseInt(style.zIndex) || 0;
            const position = style.position;
            return zIndex > 9000 && (position === 'fixed' || position === 'absolute');
        });
        
        highZIndexElements.forEach(element => {
            const style = window.getComputedStyle(element);
            const bgColor = style.backgroundColor;
            if (bgColor.includes('rgba(0, 0, 0') || bgColor.includes('rgba(0,0,0')) {
                element.style.display = 'none';
                console.log('🗑️ تم إخفاء عنصر بـ z-index عالي:', element);
            }
        });
        
        console.log(`✅ تم الانتهاء من التنظيف. تم إزالة/إخفاء ${removedOverlays} عنصر`);
    }
    
    // تشغيل التنظيف فوراً
    fullCleanup();
    
    // تشغيل التنظيف عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fullCleanup);
    }
    
    // تشغيل التنظيف عند تحميل النافذة
    window.addEventListener('load', fullCleanup);
    
    // مراقبة إضافة عناصر جديدة
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            let needsCleanup = false;
            
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const element = node;
                        const style = window.getComputedStyle(element);
                        const bgColor = style.backgroundColor;
                        const position = style.position;
                        const zIndex = parseInt(style.zIndex) || 0;
                        
                        // فحص إذا كان العنصر الجديد overlay محتمل
                        if (
                            (position === 'fixed' || position === 'absolute') &&
                            (bgColor.includes('rgba(0, 0, 0') || bgColor.includes('rgba(0,0,0')) &&
                            zIndex > 1000
                        ) {
                            needsCleanup = true;
                        }
                        
                        // فحص الكلاسات المشكوك فيها
                        if (element.classList && (
                            element.classList.contains('widdx-loading') ||
                            element.classList.contains('loading-overlay') ||
                            element.id === 'global-loading'
                        )) {
                            needsCleanup = true;
                        }
                    }
                });
            });
            
            if (needsCleanup) {
                setTimeout(fullCleanup, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('👁️ تم تفعيل مراقبة العناصر الجديدة');
    }
    
    // إضافة وظائف عامة للاستخدام
    window.fixOverlayIssue = fullCleanup;
    window.removeAllOverlays = removeAllOverlays;
    
    console.log('✅ تم تحميل إصلاح الغطاء الأسود بنجاح');
    
})();
